# Azure OpenAI
AZURE_OPENAI_ENDPOINT = "AZURE_OPENAI_ENDPOINT"
AZURE_DEPLOYMENT_CHAT = "AZURE_DEPLOYMENT_CHAT"
AZURE_DEPLOYMENT_EMBEDDING = "AZURE_DEPLOYMENT_EMBEDDING"
OPENAI_API_VERSION = "OPENAI_API_VERSION"

# Copilot
CP_AZURE_OPENAI_ENDPOINT = "CP_AZURE_OPENAI_ENDPOINT"
CP_AZURE_OPENAI_API_KEY = "CP_AZURE_OPENAI_API_KEY"

# Vector Database Type
VECTOR_DB_TYPE = "VECTOR_DB_TYPE"
DEFAULT_VECTOR_DB_TYPE = "pgvector"

# Milvus Configuration
ZILLIZ_CLOUD_URI = "ZILLIZ_CLOUD_URI"
ZILLIZ_CLOUD_API_KEY = "ZILLIZ_CLOUD_API_KEY"
PRODUCT = "PRODUCT"
PRODUCT_HEADER = "x-product"

# PGVector Configuration
PGVECTOR_CONNECTION_STRING = "PGVECTOR_CONNECTION_STRING"
PGVECTOR_HOST = "PGVECTOR_HOST"
PGVECTOR_PORT = "PGVECTOR_PORT"
PGVECTOR_DATABASE = "PGVECTOR_DATABASE"
PGVECTOR_USER = "PGVECTOR_USER"
PGVECTOR_PASSWORD = "PGVECTOR_PASSWORD"

# Proxy
PROXY_URL = "PROXY_URL"
PROXY_TOKEN = "PROXY_TOKEN"
PROXY_CONSUMER_KEY = "PROXY_CONSUMER_KEY"
PROXY_CONSUMER_SECRET = "PROXY_CONSUMER_SECRET"

# Authentication
TOKEN_ENDPOINT = "TOKEN_ENDPOINT"

# Azure Configuration
DEFAULT_AZURE_DEPLOYMENT_CHAT = "DEFAULT_AZURE_DEPLOYMENT_CHAT"
DEFAULT_OPENAI_API_VERSION = "DEFAULT_OPENAI_API_VERSION"
DEFAULT_AZURE_DEPLOYMENT_EMBEDDING = "DEFAULT_AZURE_DEPLOYMENT_EMBEDDING"
GPT_MODEL_NAME = "GPT_MODEL_NAME"

# Retrieval Configuration
K_NEIGHBOURS = 20
MAX_PROMPT_SIZE = 12000

# Reranker Configuration
ENABLED_RERANKER = "ENABLED_RERANKER"
COHERE_API_KEY = "COHERE_API_KEY"
COHERE_MODEL = "rerank-english-v3.0"
TOP_DOCS = 10

# Chat Message Types
SYSTEM = "system"
USER = "user"
