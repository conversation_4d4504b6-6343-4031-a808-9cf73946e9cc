"""
This module updates the vector database with documentation content.
It supports two different document processing approaches:
1. Release-based: Downloads and processes HTML files from release assets
2. Repository-based: Directly loads and processes markdown files from the repository

It can also process multiple products sequentially by updating environment variables.
"""

import os
import logging
import time
from docs_db_updater.application import collection_operator
from docs_db_updater.application.constants import PRODUCTS

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_all_products():
    """Update vector database for all configured products sequentially."""
    for i, product in enumerate(PRODUCTS):
        logger.info(f"\n{'='*50}")
        logger.info(f"Processing {product['PRODUCT']} ({i+1}/{len(PRODUCTS)})")
        logger.info(f"{'='*50}\n")

        # Set environment variables for this product
        for key, value in product.items():
            os.environ[key] = value
            logger.info(f"Set {key}={value}")

        # Run the updater for this product
        try:
            collection_operator.update_docs_db()
            logger.info(f"\nSuccessfully updated {product['PRODUCT']} docs")
        except Exception as e:
            logger.error(f"\nError updating {product['PRODUCT']} docs: {e}")

    logger.info("\nAll product docs updated")

if __name__ == '__main__':
    update_all_products()